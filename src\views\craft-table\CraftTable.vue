<template>
    <div class="home all-padding-12 border-box oh" style="background-color: #F2F5F8">
        <!-- 线索相关列表以及风险管理 -->
        <div class="display-flex flex-column gap-8">
            <div class="display-flex space-between all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <LeadMenus />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-18 color-black font-weight-600 b-margin-24">我的代办</span>
                <MyAgent />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-18 color-black font-weight-600 b-margin-24">企业风险监控</span>
                <CompanyRiskMonitor />
            </div>
            <div class="all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-18 color-black font-weight-600 b-margin-24">销售漏斗</span>
                <div class="w-100" style="height: 8rem; width: 100%;">
                    <div ref="funnelChartRef" style="height: 100%; width: 100%;"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed, getCurrentInstance } from 'vue'
import LeadMenus from './components/LeadMenus.vue'
import MyAgent from './components/MyAgent.vue'
import CompanyRiskMonitor from './components/CompanyRiskMonitor.vue'
import crmService from '@/service/crmService'
import { useStore } from 'vuex'

const instance = getCurrentInstance()
const echarts = instance?.appContext.config.globalProperties.$echarts
const funnelChartRef = ref<HTMLElement | null>(null)
const funnelData = ref([0, 0, 0])
const renderFunnelChart = () => {
    if (!funnelChartRef.value || !echarts) return

    const myChart = echarts.init(funnelChartRef.value)

    // 三个梯形的数据和样式
    const trapezoids = [
        { name: '线索', value: funnelData.value[0], color: '#1966FF' },
        { name: '客户', value: funnelData.value[1], color: '#60BCFF' },
        { name: '成交客户', value: funnelData.value[2], color: '#18B4AB' }
    ]

    // 梯形的尺寸设置（百分比）
    const trapezoidWidths = [
        // 第一个梯形：上边70%，下边55%
        { top: 70, bottom: 50 },  
        // 第二个梯形：上边55%，下边40%
        { top: 50, bottom: 30 },  
        // 第三个梯形：上边40%，下边25%
        { top: 30, bottom: 10 }   
    ]

    const containerWidth = funnelChartRef.value.offsetWidth || 300
    const centerX = containerWidth / 2
    const trapezoidHeight = 45
    const gap = 0
    const startY = 20

    const option = {
        tooltip: {
            trigger: 'item',
            formatter: function(params) {
                if (params.componentType === 'graphic') {
                    const index = params.name
                    return trapezoids[index].name + ': ' + trapezoids[index].value
                }
                return ''
            }
        },
        // 移除默认图例，我们将自定义图例
        graphic: trapezoids.map((trapezoid, index) => {
            const y = startY + index * (trapezoidHeight + gap)
            const topWidth = containerWidth * trapezoidWidths[index].top / 100
            const bottomWidth = containerWidth * trapezoidWidths[index].bottom / 100

            const topLeft = centerX - topWidth / 2
            const topRight = centerX + topWidth / 2
            const bottomLeft = centerX - bottomWidth / 2
            const bottomRight = centerX + bottomWidth / 2

            return [
                // 梯形
                {
                    type: 'polygon',
                    name: index,
                    shape: {
                        points: [
                            [topLeft, y],
                            [topRight, y],
                            [bottomRight, y + trapezoidHeight],
                            [bottomLeft, y + trapezoidHeight]
                        ]
                    },
                    style: {
                        fill: trapezoid.color,
                        stroke: '#fff',
                        lineWidth: 2
                    },
                    z: 10
                },
                // 只显示数值
                {
                    type: 'text',
                    style: {
                        text: trapezoid.value,
                        x: centerX,
                        y: y + trapezoidHeight / 2,
                        textAlign: 'center',
                        textVerticalAlign: 'middle',
                        fill: '#fff',
                        fontSize: 14,
                        fontWeight: 'bold'
                    },
                    z: 20
                }
            ]
        }).flat()
    }

    // 添加自定义图例（色块+文字）
    const legendY = startY + 3 * (trapezoidHeight + gap) + 20
    const legendItemWidth = 80
    const legendStartX = centerX - (trapezoids.length * legendItemWidth) / 2

    trapezoids.forEach((trapezoid, index) => {
        const legendX = legendStartX + index * legendItemWidth

        // 色块
        option.graphic.push({
            type: 'rect',
            position: [legendX, legendY],
            shape: {
                width: 12,
                height: 12
            },
            style: {
                fill: trapezoid.color
            },
            z: 10
        })

        // 文字
        option.graphic.push({
            type: 'text',
            position: [legendX + 18, legendY + 6],
            style: {
                text: trapezoid.name,
                textAlign: 'left',
                textVerticalAlign: 'middle',
                fill: '#333',
                fontSize: 12
            },
            z: 10
        })
    })

    myChart.setOption(option)
}

const getFunnelData = () => {
    crmService.stattisticsGetFunnelData().then(res => {
        console.log('漏斗图数据响应:', res)
        if (res.data && Array.isArray(res.data)) {
            const leadItem = res.data.find(item => item.label === 'lead')
            const customItem = res.data.find(item => item.label === 'custom')
            const dealCustomItem = res.data.find(item => item.label === 'dealCustom')

            funnelData.value[0] = leadItem ? leadItem.count : 0
            funnelData.value[1] = customItem ? customItem.count : 0
            funnelData.value[2] = dealCustomItem ? dealCustomItem.count : 0
            console.log('漏斗图数据:', funnelData.value)

            // 数据更新后重新渲染图表
            renderFunnelChart()
        }
    }).catch(error => {
        console.error('获取漏斗图数据失败:', error)
    })
}

const store = useStore()
const isLogin = computed(() => {
    const { isLogin } = store.state.auth
    return isLogin
})

onMounted(() => {
    console.log('isLogin', isLogin.value)
    if (isLogin.value){
        getFunnelData()
    }else{
        renderFunnelChart()
    }
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
}
</style>