<template>
    <div class="home tb-padding-12 lr-padding-16 border-box" style="background-color: #F2F5F8">
        <div class="top-bar flex-center" style="width: 100%;border: 1px solid #EBEBEB;">
            <div class="tb-padding-24 flex-center flex-column" style="color: #995A04;">
                <span>
                    <span class="font-20 font-weight-600 r-margin-4">{{ totalCount }}</span>
                    <span class="font-16">家</span>
                </span>
                <span class="font-16">线索剩余额度</span>
            </div>
        </div>
        <div class="tb-margin-16 font-18">
            全部权益
        </div>
        <div>
            <van-list v-model:loading="loading" :finished="finished" finished-text="暂无数据" @load="onLoad">
                <van-cell v-for="item in benefitList" :key="item.id">
                    <div class="display-flex top-bottom-center tb-padding-12 border-box" style="height: 2rem">
                        <div class="flex-1 display-flex flex-column top-bottom-center gap-8" style="align-items: flex-start">
                            <span class="font-18 color-black font-weight-600" >
                                {{ serviceName(item.serviceKey) }}X{{ item.totalAmount }}
                            </span>
                            <div class="display-flex space-between top-bottom-center font-16 color-two-gray" style="width: 100%">
                                <span>到期时间：{{ item.endTime ? moment(item.endTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}</span>
                            </div>
                        </div>
                        <div class="display-flex top-bottom-center" v-if="item.status && item.remainingAmount">
                            <span class="font-16" :style="{ color: getUseType(item).color }">{{ getUseType(item).type === 4 ? `剩余${item.remainingAmount}` : getUseType(item).msg }}</span>
                        </div>
                    </div>
                    <!-- <div v-if="item.isGifted" style="border: 1px solid #F2F2F2;"></div>
                    <div v-if="item.isGifted" class="display-flex font-12 t-margin-4" style="color: #FF854C;align-items: flex-start;" >来自{{ item.from }}的赠送</div> -->
                </van-cell>
            </van-list>
        </div>

    </div>
</template>

<script lang='ts' setup>
import { ref, onBeforeMount, getCurrentInstance, onMounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { tabbarheight } from '@/utils/tabbar-height'
import type { IServiceOrderPageParams, IServiceOrderResponseItem } from '@/types/order'
import orderService from '@/service/orderService'


const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})
const totalCount = ref(0)
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const type = ref('xs')
const route = useRoute()
const loading = ref<boolean>(false)
const finished = ref<boolean>(false)
const benefitList = ref<IServiceOrderResponseItem[]>([])
const queryParams = ref<IServiceOrderPageParams>({
    page: 1,
    pageSize: 10,
    serviceKey: type.value,
})
const serviceName = (key: string) => {
    if(key === 'xs'){
        return '线索联系方式'
    }
    else if(key === 'swbg'){
        return '企业财税经营分析报告'
    }
    else if(key === 'gqbg'){
        return '高新技术科技企业报告'
    }
    else if(key === 'fpbg'){
        return '企业发票数据综合分析报告'
    }else if(key === 'znwh'){
        return '智能外呼'
    }else{
        return '-'
    }
}
const getUseType = (item: IServiceOrderResponseItem) => {
    if(!item.status){
        return {
            type: 1,
            msg: '已过期',
            color:'#5DBF86',
        }
    }else if(item.remainingAmount === 0){
        return {
            type: 2,
            msg: '已使用',
            color:'#5DBF86'
        }
    }else if(item.remainingAmount === item.totalAmount){
        return {
            type: 3,
            msg: '未使用',
            color:'#FFC043'
        }
    }else{
        return {
            type: 4,
            msg: '使用中',
            color: '#5DBF86'
        }
    }
}

const getStatistic = (val:string) => {
    if(val === 'xs'){
        orderService.orderServiceStatistics({ serviceKeys: val }).then(res => {
            // console.log('res',res)
            totalCount.value = res[0].num
        })
    }else{
        orderService.orderServiceStatistics({serviceKeys:'fpbg,swbg,gqbg'}).then(res => {
            console.log('res',res)
            totalCount.value = res[0].num + res[1].num + res[2].num
        })
    }
}

const search = async (params: IServiceOrderPageParams) => {
    const res = await orderService.orderServiceOrderPage(params)
    benefitList.value.push(...res.data)
    return res
}
let ti = null
const onLoad = () => {
    
    if (ti) {
        clearTimeout(ti)
    }
    ti = setTimeout(async () => {
        const res = await search(queryParams.value)
        queryParams.value.page += 1
        loading.value = false
        console.log('benefitList.value.length',benefitList.value.length)
        if (benefitList.value.length === res.total) {
            finished.value = true
        }
    },100)
}
onBeforeMount(() => {
    console.log('route.query.type',route.query.type)
    if(route.query.type === 'bg'){
        type.value = 'fpbg,swbg,gqbg'
    }else{
        type.value = 'xs'
    }   
    queryParams.value.serviceKey = type.value
})

onMounted(() => {
    getStatistic(type.value)
})
</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

.top-bar{
    background-image: url('@/assets/hub-images/my-benefit-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

:deep(.van-cell) {
    margin-bottom: 8px;
    border-radius: 8px;
}
</style>