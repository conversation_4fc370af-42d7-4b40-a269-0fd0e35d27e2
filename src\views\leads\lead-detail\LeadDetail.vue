<script setup lang="ts">
import CICON from '@/assets/hub-images/company/company-icon.png'
import commonData from '@/js/common-data'
import crmService from '@/service/crmService'
import type { ICompanyTag } from '@/types/company'
import type { ILeadData } from '@/types/lead'
import { computed, onMounted, provide, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { LeadActivies, LeadBaseInfo, LeadGoodsMatch, LeadPolicyMatch, LeadProcess, LeadRadarChart } from './components'
import LeadIndicatorData from './components/LeadIndicatorData.vue'

const router = useRouter()
const route = useRoute()
const id = route.query.id?.toString() || ''
const socialCreditCode = ref('')
const companyDetail = ref<ILeadData | null>(null)
const active = ref(0)

const getDetail = () => {
    crmService
        .crmDetail({
            id: id,
        })
        .then((res) => {
            companyDetail.value = res
            socialCreditCode.value = res.socialCreditCode
        })
}

const formatTags = (tgas?: ICompanyTag[]) => {
    if (!tgas) return []
    return tgas.filter((e) => e.categoryCode !== '007' && e.categoryCode !== '001')
}

const toCompany = (socialCreditCode: string) => {
    if (!socialCreditCode) return
    router.push({
        name: 'company-detail',
        query: {
            socialCreditCode: socialCreditCode,
        },
    })
}

const isCustomerDetail = computed(() => {
    return route.name === 'customer-detail'
})

onMounted(() => {
    getDetail()
})

provide('socialCreditCode', socialCreditCode)
</script>
<template>
    <van-skeleton title :row="3" v-if="!companyDetail" />
    <div class="flex flex-column gap-12 back-color-main flex-1 company-detail" v-if="companyDetail">
        <div class="flex flex-column back-color-white all-padding-12 gap-12">
            <div class="flex flex-row gap-8">
                <div class="h-48 w-48">
                    <img :src="CICON" alt="" class="height-100 wight-100" />
                </div>
                <div class="flex flex-column gap-4">
                    <div class="flex flex-row space-between b-margin-6">
                        <div class="font-16 color-black font-weight-500 flex-1">
                            {{ companyDetail?.name }}
                        </div>
                        <div class="font-12 back-color-blue color-white h-20 flex center">转成新客户</div>
                    </div>
                    <div class="font-16 flex flex-row gap-8 oh top-bottom-center flex-wrap">
                        <div
                            class="small-tag tag-blue"
                            v-for="tag in formatTags(companyDetail.customFields?.companyTags)"
                            :key="tag.tagName"
                        >
                            {{ tag.tagName }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-column font-14 space-around all-padding-12 back-color-second-blue border-radius-4">
                <div class="color-text-grey">
                    公司名称：<span class="color-blue" @click="toCompany(companyDetail?.socialCreditCode)">{{
                        companyDetail?.companyName
                    }}</span>
                </div>
                <div class="color-text-grey">
                    负责人：<span class="color-black">{{ companyDetail?.user }}</span>
                </div>
                <div class="color-text-grey">
                    手机号：<span class="color-black">{{ companyDetail?.contactInfo.mobile }}</span>
                </div>
                <div class="color-text-grey">
                    跟进状态：<span class="color-black">{{ companyDetail?.statusStr }}</span>
                </div>
            </div>

            <LeadProcess :data="companyDetail" v-if="isCustomerDetail" />
        </div>
        <div class="flex-1">
            <LeadActivies :leadId="id" :companyName="companyDetail?.companyName" />
        </div>
        

        <div class="flex flex-column gap-12 flex-1">
            <van-tabs v-model:active="active" class="width-100">
                <van-tab v-for="model in commonData.leadsTabs" :key="model.value" :title="model.title"></van-tab>
            </van-tabs>
            <div class="flex flex-1 back-color-white all-padding-12">
                <div v-if="active === 0" class="width-100">
                    <div class="flex flex-column gap-8">
                        <div v-if="companyDetail.basicScore" class="font-14 color-black">企业基础能力评分</div>
                        <div v-if="companyDetail.basicScore" class="font-16 color-black font-weight-700">
                            {{ companyDetail.basicScore }}分
                        </div>
                    </div>
                    <div class="flex flex-column">
                        <LeadBaseInfo :data="companyDetail" />
                        <LeadRadarChart :socialCreditCode="socialCreditCode" />
                        <LeadIndicatorData :socialCreditCode="socialCreditCode" :name="companyDetail.companyName" />
                    </div>
                </div>
                <div v-if="active === 1" class="width-100">
                    <LeadGoodsMatch :companyId="companyDetail.companyId" />
                </div>
                <div v-if="active === 2" class="width-100">
                    <LeadPolicyMatch :companyId="companyDetail.companyId" />
                </div>
            </div>
        </div>
    </div>
</template>
<style lang="scss" scoped>
.company-detail :deep(.van-tabs__nav) {
    background-color: transparent;
}

.company-detail .section-title {
    border-bottom: 1px solid var(--border-color);
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 12px;
}

.company-detail .module-item {
    width: 25%;
    padding: 24px 0 14px 0;
    position: relative;
    border-bottom: 0.6px solid #eee;
}

.company-detail .module-item-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-right: 0.6px solid #eee;
}

.company-detail .module-item:nth-child(4n) .module-item-container {
    border-right: 0;
}

.company-detail .module-item-count {
    position: absolute;
    right: 8px;
    top: 8px;
    font-size: 13px;
}

.company-detail :deep(.van-tab--grow) {
    padding: 0 6px;
}

.company-detail :deep(.van-sticky--fixed) .van-tabs__nav {
    background-color: white;
}
</style>
